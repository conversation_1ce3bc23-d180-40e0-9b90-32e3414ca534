package com.ecco.acceptancetests.ui.pages.referral;

import java.util.ArrayList;
import java.util.List;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import com.ecco.acceptancetests.ui.pages.BasePageObject;
import com.ecco.acceptancetests.ui.pages.supportplan.ReviewSetupPage;
import com.ecco.acceptancetests.ui.pages.supportplan.SupportPlanPage;

import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

public class ListClientsPage extends BasePageObject {

    private static final String URL = "/dynamic/secure/referralFlow";

    private static final String LINK_NEWCLIENT = "new client";

    private static final String TAG_CLIENT_TABLE_ROW = "tr";

    public ListClientsPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public List<String> getVisibleClientNames() {
        List<String> clientNames = new ArrayList<>();
        //List<WebElement> referrals = getWebDriver().findElements(By.tagName(TAG_REFERRAL_TABLE_ROW));
        List<WebElement> clients = getWebDriver().findElements(By.xpath(".//td[2]"));
        for (WebElement ref : clients) {
            String listedName = getClientNameAsListed(ref);
            clientNames.add(listedName);
        }
        return clientNames;
    }

    public void useNewClient() {
        clickLink(LINK_NEWCLIENT);
    }

    public void useExistingClient(String firstName, String lastName) {
        clickLink(firstName + " " + lastName);
    }
    public void useExistingClient(String displayName) {
        clickLink(displayName);
    }

    public Long getClientId(String firstName, String lastName) {
        UriComponents builder = UriComponentsBuilder.fromUriString(getWebDriver().findElement(By.linkText(firstName + " " + lastName)).getAttribute("href")).build();
        return Long.parseLong(builder.getQueryParams().getFirst("clientId"));
    }

    private static String getClientNameAsListed(WebElement referral) {
        String refListName = referral.findElement(By.xpath("//td[2]")).getText();
        return refListName;
    }

    public SupportPlanPage openSupportPlan(String firstName, String lastName) {
        clickLink(lastName + ", " + firstName);
        return new SupportPlanPage(getWebDriver());
    }

    public ReviewSetupPage openReviewSetupPage(String firstName, String lastName) {
        clickLink(lastName + ", " + firstName);
        return new ReviewSetupPage(getWebDriver());
    }

}
