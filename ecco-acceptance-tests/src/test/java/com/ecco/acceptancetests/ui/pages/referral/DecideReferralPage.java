package com.ecco.acceptancetests.ui.pages.referral;

import static org.junit.Assert.assertTrue;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public class DecideReferralPage extends EccoBasePage {

    private static final String URL = "/dynamic/secure/referralAspectFlow";

    private static final String BUTTON_ACCEPTSELF_ID = "_eventId_acceptSelf";
    private static final String BUTTON_SIGNPOST_ID = "_eventId_signpost";

    private static final String VERIFY_TEXT1 = "accept";

    ReferralViewPage referralPage;
    public DecideReferralPage(ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
    }

    @Override
    public void verifyIsCurrentPage() {
        super.verifyIsCurrentPage();
        WebElement bodyText = getWebDriver().findElement(By.tagName("body"));
        assertTrue("Incorrect page found - expected to see text " + VERIFY_TEXT1, bodyText.getText().contains(VERIFY_TEXT1));
    }

    public void type(boolean accept) {
        if (accept) {
            if (!getWebDriver().findElements(By.name(BUTTON_ACCEPT)).isEmpty()) {
                clickButton(BUTTON_ACCEPT);
            } else {
                clickButtonById(BUTTON_ACCEPT);
            }
        } else {
            clickButton(BUTTON_SIGNPOST_ID);
        }
    }

    @Override
    public EccoBasePage defaultAction() {
        type(true);
        return referralPage;
    }

}
