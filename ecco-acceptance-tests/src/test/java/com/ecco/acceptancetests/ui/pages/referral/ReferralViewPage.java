package com.ecco.acceptancetests.ui.pages.referral;

import static org.junit.Assert.assertEquals;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public abstract class ReferralViewPage extends EccoBasePage {

    protected static final String URL = "/online/referrals/";

    public static final String SID_CLIENT_NAME = "sid_clientName";
    public static final String SID_REFERRAL_ID = "sid_rid";

    private static final String LINK_FLOW = "#tasks-tab";
    private static final String LINK_TEXT_SUPPORT_HISTORY = "support history";
    private static final String LINK_TEXT_RISK_HISTORY = "risk history";

    // Links in the main page header
    private static final String LINK_COMMENTS = "referral comments";
    private static final String LINK_APPOINTMENTS = "calendar";
    private static final String LINK_ATTACHMENTS = "attachments";
    private static final String LINK_CLIENT_DETAILS = "client details";
    private static final String LINK_EMERGENCY_DETAILS = "emergency details";
    private static final String LINK_CONTACTINFO = "contacts";
    private static final String LINK_PRINT = "printable";

    // all the aspects for all services
    // this will cause a problem
    protected static final String LINK_DESTINATION = "destination of referral";
    protected static final String LINK_SOURCE = "source of referral";
    protected static final String LINK_DETAILS = "details of referral";
    protected static final String LINK_PENDING = "pending status";
    protected static final String LINK_DECIDEREFERRAL = "appropriate referral";
    protected static final String LINK_ACCOMMODATION = "accommodation";
    protected static final String LINK_SETUPASSESSMENT = "setup initial assessment";
    protected static final String LINK_NEEDSASSESSMENT = "needs assessment";
    protected static final String LINK_DECIDESERVICE = "accept on service";
    protected static final String LINK_START = "start on service";
    protected static final String LINK_SCHEDULEREVIEWS = "schedule reviews";
    protected static final String LINK_SUPPORTPLAN = "support plan";
    protected static final String LINK_OUTCOMESTAR = "family outcome star";
    protected static final String LINK_REVIEW = "review";
    protected static final String LINK_RISKMANAGEMENT = "risk management";
    protected static final String LINK_SOCIALIMPACT = "social impact";
    protected static final String LINK_SERVICEAGREEMENT = "service agreements";
    protected static final String LINK_CLOSE = "close off"; // NB users more comfortable with 'close off'
    protected static final String LINK_FUNDING = "funding";
    protected static final String LINK_SUPPORTSTAFFNOTES = "notes";
    protected static final String LINK_DELIVERED_BY = "delivered by";

    protected ReferralViewPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public void checkReferralName(String expected) {
        assertEquals("Referral name not correct", expected, findElementSoon(By.id(SID_CLIENT_NAME)).getText());
    }

    public long getReferralId() {
        return Long.parseLong(findElementSoon(By.id(SID_REFERRAL_ID)).getText());
    }


    // ***** HEADER LINKS *****

    public void comments() {
        clickLink(LINK_COMMENTS);
    }
    public void appointments() {
        clickLink(LINK_APPOINTMENTS);
    }
    public void attachments() {
        clickLink(LINK_ATTACHMENTS);
    }
    public void clientDetails() {
        clickLink(LINK_CLIENT_DETAILS);
    }
    public void emergencyDetails() {
        clickLink(LINK_EMERGENCY_DETAILS);
    }
    public void contactInfo() {
        clickLink(LINK_CONTACTINFO);
    }
    public void printable() {
        clickLink(LINK_PRINT);
    }


    // ***** TASKS (aka Referral Aspects) *****

    public void selectFlowTab() {
        clickLinkHref(LINK_FLOW);
    }

    public void selectSupportHistoryTab() {
        clickLink(LINK_TEXT_SUPPORT_HISTORY);
    }

    public void selectRiskHistoryTab() {
        clickLink(LINK_TEXT_RISK_HISTORY);
    }

    protected void clickTasksLink(String linkText) {
        selectFlowTab();
        clickLink(linkText);
    }

    protected void clickTasksButton(String buttonText) {
        selectFlowTab();
        clickButtonByText(buttonText);
    }

    public void destination() {
        selectFlowTab();
        clickTasksButton(LINK_DESTINATION);
    }

    public void source() {
        clickTasksButton(LINK_SOURCE);
    }
    public void dataProtectionAspect() {
        selectFlowTab();
        clickById("ra-dataProtection");
    }
    public void emergencyDetailsAspect() {
        clickById("ra-emergencyDetails");
    }
    public void deliveredBy() {
        clickTasksButton(LINK_DELIVERED_BY);
    }

    public void details() {
        clickTasksButton(LINK_DETAILS);
    }
    public void pendingStatus() {
        clickTasksButton(LINK_PENDING);
    }
    public void decideReferral() {
        clickTasksButton(LINK_DECIDEREFERRAL);
    }
    public void accommodation() {
        clickTasksButton(LINK_ACCOMMODATION);
    }
    public void setupAssessment() {
        clickTasksButton(LINK_SETUPASSESSMENT);
    }
    public void funding() {
        clickTasksButton(LINK_FUNDING);
    }
    public void needsAssessment() {
        clickTasksButton(LINK_NEEDSASSESSMENT);
        // small pause to allow rendering, otherwise tabs can't be found
        waitFor(1000);
    }
    public void decideService() {
        clickTasksButton(LINK_DECIDESERVICE);
    }
    public void start() {
        clickTasksButton(LINK_START);
    }
    public void supportStaffNotes() {
        clickTasksButton(LINK_SUPPORTSTAFFNOTES);
    }
    public void scheduleReviews() {
        clickTasksButton(LINK_SCHEDULEREVIEWS);
    }
    public void supportPlan() {
        clickTasksButton(LINK_SUPPORTPLAN);
    }
    public void generalQuestionnaireOutcomeStar() {
        clickTasksButton(LINK_OUTCOMESTAR);
    }
    public void review() {
        clickTasksButton(LINK_REVIEW);
    }
    public void riskManagement() {
        clickTasksButton(LINK_RISKMANAGEMENT);
    }
    public void socialImpact() {
        clickTasksButton(LINK_SOCIALIMPACT);
    }
    public void serviceAgreement() {clickTasksButton(LINK_SERVICEAGREEMENT);}
    public void exited() {clickTasksButton(LINK_CLOSE);}

    public boolean isDetailsEnabled() {
        return !getWebDriver().findElements(By.linkText(LINK_DETAILS)).isEmpty();
    }
}
