package com.ecco.acceptancetests.ui.pages.referral;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;
import com.ecco.data.client.ReferralOptions;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.Select;

public class DestinationPage extends EccoBasePage {

    private static final String URL = "/dynamic/secure/referralAspectFlow";

    ReferralViewPage referralPage;
    ReferralOptions options;

    public DestinationPage(ReferralOptions options, ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
        this.options = options;
    }

    /**
     * Default data - pick the first project.
     */
    @Override
    public EccoBasePage defaultAction() {

        try {
            WebElement element = findElementSoon(By.linkText("project 6"));
            element.click();
        }
        catch (Exception e) {
            // fall back to <select> version
            // Pick the first project
            final WebElement listElement = findElementSoon(By.name("project"));
            // For single option, we can use shorter version
            new Select(listElement).selectByIndex(1);
        }

        clickButtonByText(EccoBasePage.BUTTON_SAVE);
        return referralPage;
    }

}
