package com.ecco.acceptancetests.ui.pages;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

import static java.util.stream.Collectors.toList;

public enum Role {

    site_sysadmin,
    senior_manager,
    manager,
    staff,
    client,
    commisioner,
    login,
    demo,
    useradmin,
    reports,
    sysadmin;

    public static List<String> rolesToNames(Role... rolesArg) {
        return Arrays.stream(rolesArg)
                .map(role -> StringUtils.replace(role.name(), "_", " "))
                .collect(toList());
    }

}
