package com.ecco.acceptancetests.ui.pages.referral;

import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public class DecideServicePage extends EccoBasePage {

    protected static final String URL = "/dynamic/secure/referralAspectFlow";

    private static final String BUTTON_ACCEPT_ID = "accept";
    private static final String BUTTON_SIGNPOST_ID = "signpost";

    ReferralViewPage referralPage;
    public DecideServicePage(ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
    }

    public void decision(boolean accept) {
        if (accept) {
            clickButtonById(BUTTON_ACCEPT_ID);
        } else {
            clickButtonById(BUTTON_SIGNPOST_ID);
        }
    }

    @Override
    public EccoBasePage defaultAction() {
        decision(true);
        return referralPage;
    }

}
