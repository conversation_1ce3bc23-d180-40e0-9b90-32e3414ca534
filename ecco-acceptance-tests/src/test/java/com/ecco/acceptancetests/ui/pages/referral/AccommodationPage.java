package com.ecco.acceptancetests.ui.pages.referral;

import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public class AccommodationPage extends EccoBasePage {

    private static final String URL = "/dynamic/secure/referralAspectFlow";

    private static final String DROPDOWN_PROJECT_NAME = "accommodation";

    ReferralViewPage referralPage;
    public AccommodationPage(ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
    }

    public void chooseProject(String project) {
        // SKIP for now as we often already have this sorted
//        setSelection(DROPDOWN_PROJECT_NAME, project);
        clickButtonByText(BUTTON_CANCEL);
    }

    @Override
    public EccoBasePage defaultAction() {
        chooseProject("unit 5-1");
        return referralPage;
    }

}
