package com.ecco.acceptancetests.ui.pages.referral;

import com.ecco.data.client.ReferralOptions;
import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public class DataProtectionPage extends EccoBasePage {

    private static final String URL = "/dynamic/secure/referralFlow.html";

    ReferralViewPage referralPage;
    ReferralOptions options;

    public DataProtectionPage(ReferralOptions options, ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
        this.options = options;
    }

    /**
     * Default data - none needed, so simply hit the 'accept' button
     */
    @Override
    public EccoBasePage defaultAction() {
        verifyIsCurrentPage();
        clickById(EccoBasePage.BUTTON_ACCEPT);
        return referralPage;
    }

}
