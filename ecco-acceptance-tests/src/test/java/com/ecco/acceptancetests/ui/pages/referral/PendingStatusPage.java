package com.ecco.acceptancetests.ui.pages.referral;

import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public class PendingStatusPage extends EccoBasePage {

    private static final String URL = "/dynamic/secure/referralAspectFlow";

    private static final String DROPDOWN_STATUS_NAME = "pendingStatus";

    ReferralViewPage referralPage;
    public PendingStatusPage(ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
    }

    public void choosePendingStatus(String status) {
        setSelection(DROPDOWN_STATUS_NAME, status);
        clickButtonByText(EccoBasePage.BUTTON_SAVE);
    }

    @Override
    public EccoBasePage defaultAction() {
        choosePendingStatus("paperwork missing");
        return referralPage;
    }

}
