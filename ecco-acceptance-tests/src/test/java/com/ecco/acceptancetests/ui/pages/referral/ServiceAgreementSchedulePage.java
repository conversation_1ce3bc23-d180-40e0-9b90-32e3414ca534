package com.ecco.acceptancetests.ui.pages.referral;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;
import com.ecco.data.client.ReferralOptions;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;
import org.openqa.selenium.WebDriver;

public class ServiceAgreementSchedulePage extends EccoBasePage {
    private static final String URL = "/dynamic/secure/entity/appointmentSchedule/edit.html?agreementId=";

    private static final String BUTTON_SAVE_NAME = "_save";
    private static final String BACK_LINK = "back";
    private static final String END_FIELD = "end";
    private static final String SCHEDULES_TAB_HREF = "#fragment-2";
    private static final String NEW_SCHEDULE_LINK = "new appointment schedule";
    private static final String TIME_FIELD = "time";
    private static final String APPOINTMENT_TYPE_DROPDOWN = "appointmentType";
    private static final String DURATION_FIELD = "agreedDurationInMinutes";
    private static final String CHARGE_FIELD = "charge";
    private static final String SUNDAY_CHECKBOX = "days.sunday";
    private static final String MONDAY_CHECKBOX = "days.monday";
    private static final String TUESDAY_CHECKBOX = "days.tuesday";
    private static final String WEDNESDAY_CHECKBOX = "days.wednesday";
    private static final String THURSDAY_CHECKBOX = "days.thursday";
    private static final String FRIDAY_CHECKBOX = "days.friday";
    private static final String SATURDAY_CHECKBOX = "days.saturday";

    ReferralViewPage referralPage;
    ReferralOptions options;

    public ServiceAgreementSchedulePage(ReferralOptions options, ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
        this.options = options;
    }

    @Override
    public EccoBasePage defaultAction() {
        createAppointmentSchedule(new LocalDate(), 30, 10, 1);
        return referralPage;
    }

    private void createAppointmentSchedule(LocalDate start, int days, int costPerHour, int hoursPerAppointment) {
        // Click 'schedules' tab
        clickLinkHref(SCHEDULES_TAB_HREF);
        clickLink(NEW_SCHEDULE_LINK);
        setSelection(APPOINTMENT_TYPE_DROPDOWN, options.withAgreementAppointmentType());
        setField(DURATION_FIELD, String.valueOf(hoursPerAppointment * 60));
        setField(CHARGE_FIELD, String.valueOf(costPerHour));
//        setDate(START_FIELD, start.toDateTimeAtStartOfDay().toDate());
        setDate(END_FIELD, start.plusDays(days - 1).toDateTimeAtStartOfDay().toDate());
        setTimePickerField(TIME_FIELD, new LocalTime().withHourOfDay(11).withMinuteOfHour(0));
        setCheckbox(SUNDAY_CHECKBOX, true);
        setCheckbox(MONDAY_CHECKBOX, true);
        setCheckbox(TUESDAY_CHECKBOX, true);
        setCheckbox(WEDNESDAY_CHECKBOX, true);
        setCheckbox(THURSDAY_CHECKBOX, true);
        setCheckbox(FRIDAY_CHECKBOX, true);
        setCheckbox(SATURDAY_CHECKBOX, true);
        clickButton(BUTTON_SAVE_NAME);
        clickLink(BACK_LINK);
        clickLink(BACK_LINK);
    }

}
