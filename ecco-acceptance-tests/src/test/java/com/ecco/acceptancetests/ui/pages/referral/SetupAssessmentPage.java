package com.ecco.acceptancetests.ui.pages.referral;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

import org.joda.time.DateTime;
import org.openqa.selenium.WebDriver;

public class SetupAssessmentPage extends EccoBasePage {
    private static final String URL = "/dynamic/secure/referralAspectFlow";

    private static final String BUTTON_SAVE = "save";
    private static final String DROPDOWN_INTERVIEWER_1 = "interviewer1";
    private static final String FIELD_ASSESSMENT_DATE = "decisionDate";

    ReferralViewPage referralPage;
    public SetupAssessmentPage(ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
    }

    public void setupInterview(String interviewer, DateTime assessmentDate) {
        setSelection(DROPDOWN_INTERVIEWER_1, interviewer);
        setDateTime(FIELD_ASSESSMENT_DATE, assessmentDate);
        clickButtonByText(BUTTON_SAVE);
    }

    @Override
    public EccoBasePage defaultAction() {
        setupInterview("sysadmin", new DateTime());
        return referralPage;
    }

}
