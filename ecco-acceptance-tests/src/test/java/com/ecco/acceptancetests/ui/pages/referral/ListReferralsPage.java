package com.ecco.acceptancetests.ui.pages.referral;

import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import com.ecco.acceptancetests.ui.pages.BasePageObject;
import com.ecco.acceptancetests.ui.pages.supportplan.ReviewSetupPage;
import com.ecco.acceptancetests.ui.pages.supportplan.SupportPlanPage;

import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

public class ListReferralsPage extends BasePageObject {

    private static final String URL = "/dynamic/secure/referralFlow";

    private static final String BUTTON_ID_WAITING_LIST = "showWaitingList";
    private static final String BUTTON_ID_SHOW_INCOMPLETE = "showIncomplete";
    private static final String BUTTON_ID_SHOW_ALL = "showAll";

    private static final String TAG_REFERRAL_TABLE_ROW = "tr";

    public ListReferralsPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public List<String> getVisibleReferralNames() {
        List<String> referralNames = new ArrayList<>();
        //List<WebElement> referrals = getWebDriver().findElements(By.tagName(TAG_REFERRAL_TABLE_ROW));
        List<WebElement> referrals = findElementsSoon(By.xpath(".//td[2]"));
        for (WebElement ref : referrals) {
            //String listedName = getReferralNameAsListed(ref);
            String displayName = extractDisplayName(ref.getText());
            referralNames.add(displayName);
        }
        return referralNames;
    }

    private static String extractDisplayName(String refListName) {
        String displayName = StringUtils.split(refListName, ", ")[1] + " " + StringUtils.split(refListName, ", ")[0];
        return displayName;
    }

    public void showWaitingList() {
        clickButton(BUTTON_ID_WAITING_LIST);
    }

    public void showIncompleteReferrals() {
        clickButton(BUTTON_ID_SHOW_INCOMPLETE);
    }

    public void showAllReferrals() {
        clickButton(BUTTON_ID_SHOW_ALL);
    }

    public void openReferral(String firstName, String lastName) {
        clickLink(lastName + ", " + firstName);
    }

    public void openReferral(String code) {
        clickLink(getReferralNameFromCode(code));
    }

    public Long getReferralId(String firstName, String lastName) {
        UriComponents builder = UriComponentsBuilder.fromUriString(getWebDriver().findElement(By.linkText(lastName + ", " + firstName)).getAttribute("href")).build();
        return Long.parseLong(builder.getQueryParams().getFirst("referralId"));
    }

    public String getReferralNameFromCode(String code) {
        List<WebElement> referrals = getWebDriver().findElements(By.tagName(TAG_REFERRAL_TABLE_ROW));
        for (WebElement ref : referrals) {
            String c = getReferralCode(ref);
            if (StringUtils.equalsIgnoreCase(code, c)) {
                return getReferralNameAsListed(ref);
            }
        }
        return null;
    }

    private static String getReferralNameAsListed(WebElement referral) {
        String refListName = referral.findElement(By.xpath("//td[2]")).getText();
        return refListName;
    }
    private static String getReferralCode(WebElement referral) {
        String code = referral.findElement(By.xpath("//td[1]")).getText();
        return code;
    }

    public SupportPlanPage openSupportPlan(String firstName, String lastName) {
        clickLink(lastName + ", " + firstName);
        return new SupportPlanPage(getWebDriver());
    }

    public ReviewSetupPage openReviewSetupPage(String firstName, String lastName) {
        clickLink(lastName + ", " + firstName);
        return new ReviewSetupPage(getWebDriver());
    }

    /*
    // default action opens the first referral
    @Override
    public EccoBasePage defaultAction() {
        String firstReferral = getVisibleReferralNames().get(0);
        clickLink(firstReferral);
        return new TaskDefinitionsService1(getWebDriver());
    }
    */

    /*
    public DashboardTab openSupportPlan() {
        *
         * may want to extend this, to click the support plan for a particular referral?
         * Shouldn't be a problem, should only be one matching result in tests, but maybe something
         * to consider.
         *
         * For info, link href looks like this:
         * /ecco/dynamic/secure/supportPlanFlow.html?referralId=1&clientId=1&_SECURITY_STATE_= ...
         *
        clickLink(LINK_OPEN_SUPPORT_PLAN);
        return new DashboardTab(getWebDriver());
    }
    */

    /*
    public String checkSupportPlanClosureReason() {
        // we duplicate the tooltip text in alt attribute
        WebElement element = getWebDriver().findElement(By.className("tooltipImage"));
        String tooltip = element.getAttribute("alt");


        String[] text = tooltip.split("\\:");
        assertEquals("Support plan not listed as 'exited' as expected", 3, text.length);
        assertEquals("Support plan not listed as 'exited'", "exited on", text[0].trim());
        return text[2].trim();
    }
    */

}
