package com.ecco.acceptancetests.ui.pages.referral;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

import com.ecco.data.client.ReferralOptions;
import org.joda.time.LocalDate;
import org.openqa.selenium.WebDriver;

public class ServiceAgreementPage extends EccoBasePage {
    private static final String URL = "xxx";

    private static final String BUTTON_SAVE_NAME = "_save";
    private static final String NEW_AGREEMENT_LINK = "new agreement";
    private static final String START_FIELD = "start";
    private static final String END_FIELD = "end";
    private static final String AGREEMENT_HOURS_FIELD = "agreementHours";
    private static final String AGREEMENT_CHARGE_FIELD = "agreementCharge";

    ReferralViewPage referralPage;
    ReferralOptions options;
    ServiceAgreementSchedulePage schedulePage;

    public ServiceAgreementPage(ReferralOptions options, ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
        this.options = options;
        this.schedulePage = new ServiceAgreementSchedulePage(options, referralPage, webDriver);
    }

    @Override
    public EccoBasePage defaultAction() {
        createAgreement(new LocalDate(), 30, 10, 1);
        this.schedulePage.defaultAction();
        return referralPage;
    }

    private void createAgreement(LocalDate start, int days, int costPerHour, int hoursPerAppointment) {
        clickLink(NEW_AGREEMENT_LINK);
        setDate(START_FIELD, start.toDateTimeAtStartOfDay().toDate());
        setDate(END_FIELD, start.plusDays(days - 1).toDateTimeAtStartOfDay().toDate());
        setField(AGREEMENT_HOURS_FIELD, String.valueOf(days * hoursPerAppointment));
        setField(AGREEMENT_CHARGE_FIELD, String.valueOf(days * hoursPerAppointment * costPerHour));
        clickButton(BUTTON_SAVE_NAME);
    }

}
