package com.ecco.acceptancetests.ui.pages.referral;

import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public class TaskDefinitionsService1 extends ReferralViewPage {

    private static final String OVERVIEW_TAB_HREF = "#fragment-0";

    public TaskDefinitionsService1(WebDriver webDriver) {
        super(webDriver);
    }

    // no default action really, but could 'step through' some stack of pages holding the taskDefinitions
    @Override
    public EccoBasePage defaultAction() {
        return null;
    }

    public void selectOverviewTab() {
        clickLinkHref(OVERVIEW_TAB_HREF);
    }

}
