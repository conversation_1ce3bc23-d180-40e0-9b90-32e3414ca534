package com.ecco.acceptancetests.ui.pages.referral;

import static org.junit.Assert.assertTrue;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import com.ecco.acceptancetests.ui.pages.BasePageObject;

public class FindReferralsPage extends BasePageObject {

    private static final String URL = "/dynamic/secure/referralFlow";

    private static final String SUB_HEADER_TEXT = "enter any of the client's details";
    private static final String MATCHCLIENT_HEADER_TEXT = "there are matches";
    private static final String PROBLEM_HEADER_TEXT = "there's a problem";
    private static final String MATCH_HEADER_TEXT = "matched a known client: check your referral hasn't already been processed";
    private static final String NOMATCH_HEADER_TEXT = "no matches - a new client: please enter more details";

    private static final String FIELD_CODE = "code";
    private static final String FIELD_FIRSTNAME = "contact.firstName";
    private static final String FIELD_LASTNAME = "contact.lastName";
    private static final String FIELD_BIRTHDATE_DAY = "birthDate.day";
    private static final String FIELD_BIRTHDATE_MONTH = "birthDate.month";
    private static final String FIELD_BIRTHDATE_YEAR = "birthDate.year";
    private static final String RADIO_FIELD_GENDER = "gender";
    private static final String FIELD_NATIONAL_INSURANCE = "ni";
    private static final String FIELD_NHS_NUMBER = "nhs";
    private static final String FIELD_ECCO_USERNAME = "username";

    private static final String BUTTON_FIND_ID = "_eventId_search";
    private static final String LINK_CONTINUE_TEXT = "continue new referral";

    public FindReferralsPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    @Override
    public void verifyIsCurrentPage() {
        super.verifyIsCurrentPage();
        WebElement bodyText = getWebDriver().findElement(By.tagName("body"));
        assertTrue("Incorrect page found - expected to see text " + SUB_HEADER_TEXT,
                bodyText.getText().contains(SUB_HEADER_TEXT));
    }

    /**
     * If pass null on any param, we expect that to be skipped by underlying setField/Selection/Radio
     */
    public void find(String cid, String firstName, String lastName, String dobDay, String dobMonth, String dobYear,
            String gender, String niNumber, String nhsNumber, String eccoUsername) {
        setField(FIELD_CODE, cid);
        setField(FIELD_FIRSTNAME, firstName);
        setField(FIELD_LASTNAME, lastName);
        setSelection(FIELD_BIRTHDATE_DAY, dobDay);
        setSelection(FIELD_BIRTHDATE_MONTH, dobMonth);
        setSelection(FIELD_BIRTHDATE_YEAR, dobYear);
        setSelection(RADIO_FIELD_GENDER, gender);
        setField(FIELD_NATIONAL_INSURANCE, niNumber);
        setField(FIELD_NHS_NUMBER, nhsNumber);
        setField(FIELD_ECCO_USERNAME, eccoUsername);

        clickButton(BUTTON_FIND_ID);

        // Wait for the spinner to be removed so we have a definitive response before we continue
        waitWhileElementDisplayed("loading gif never disappeared", By.id("spinning"));
    }

    public enum ExistingClientOutcome {
        NO_MATCH,
        MATCH,
        PROBLEM
    }

    // either we see existing clients: "there are matches"
    // or we see messages from hasExistingReferrals
    public ExistingClientOutcome hasExistingClients(boolean expectedClientExisting) {
        WebElement bodyText = getWebDriver().findElement(By.tagName("body"));
        boolean existing = bodyText.getText().contains(MATCHCLIENT_HEADER_TEXT);
        assertTrue("client match expectations not met", (expectedClientExisting && existing) || (!expectedClientExisting && !existing));
        if (bodyText.getText().contains(PROBLEM_HEADER_TEXT)) {
            return ExistingClientOutcome.PROBLEM;
        }
        return existing? ExistingClientOutcome.MATCH : ExistingClientOutcome.NO_MATCH;
    }

    // either we see existing referrals: "matched a known client: check your referral hasn't already been processed"
    // or we see "no matches - a new client: please enter more details"
    public boolean hasExistingReferrals(boolean expectedClientExisting) {
        WebElement bodyText = getWebDriver().findElement(By.id("status-message"));
        boolean existing = bodyText.getText().contains(MATCH_HEADER_TEXT);
        boolean newNotExisting = bodyText.getText().contains(NOMATCH_HEADER_TEXT);
        // assert that we have one page or the other
        assertTrue("Incorrect page found - expected to see text one of 'no matches' or 'matched a known client'", existing | newNotExisting);
        // check our expectations
        assertTrue("referral match expectations not met", (expectedClientExisting && existing) || (!expectedClientExisting && !existing));
        return existing;
    }

    public void continueNewReferral() {
        clickLink(LINK_CONTINUE_TEXT);
    }

}
