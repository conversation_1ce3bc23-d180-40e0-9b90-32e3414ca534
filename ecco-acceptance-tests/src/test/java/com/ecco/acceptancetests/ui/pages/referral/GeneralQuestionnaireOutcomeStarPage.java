package com.ecco.acceptancetests.ui.pages.referral;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;
import com.ecco.acceptancetests.ui.pages.supportplan.EvidenceBasePage;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class GeneralQuestionnaireOutcomeStarPage extends EvidenceBasePage {

    private static final String URL = "/dynamic/secure/generic/generalQuestionnaire/edit.html";

    ReferralViewPage referralPage;

    public GeneralQuestionnaireOutcomeStarPage(ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
    }

    public void checkStarCorrect() {
        waitForVisibleElementById("evidence-container");
        List<WebElement> textElements = findElementsSoon(By.xpath("//*[local-name() = 'svg']//*[name()='text']"));
        // 77 being 10 scores and a label for each arm, 7 arms = 77 (but 82 is probably due to multi-line labels)
        assertThat(textElements.size()).isEqualTo(82).describedAs("Can't find all the text nodes expected in the star");
    }

    /**
     * Default data - pick the first project.
     */
    public EccoBasePage defaultAction() {
        notes(new Date());
        return referralPage;
    }

    private void notes(final Date notesDate) {
        setWorkDate(notesDate);
        setComment("star notes comment");
        clickButtonByText("save");
    }

}
