package com.ecco.acceptancetests.ui.pages.referral;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;
import org.openqa.selenium.WebDriver;

public class DeliveredByPage extends EccoBasePage {

    private static final String URL = "/dynamic/secure/referralAspectFlow";

    ReferralViewPage referralPage;

    public DeliveredByPage(ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
    }

    /**
     * Default data - pick the first project.
     */
    @Override
    public EccoBasePage defaultAction() {
        clickButton(BUTTON_SAVE);
        return referralPage;
    }

}
