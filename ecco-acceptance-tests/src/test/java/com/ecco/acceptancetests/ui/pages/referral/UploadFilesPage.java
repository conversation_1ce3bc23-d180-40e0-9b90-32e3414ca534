package com.ecco.acceptancetests.ui.pages.referral;

import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public class UploadFilesPage extends EccoBasePage {

    protected static final String URL = "/dynamic/secure/referralAspectFlow";

    private static final String LINK_NEXT = "next";
    private static final String FIELD_UPLOAD_FILE = "file";

    ReferralViewPage referralPage;
    public UploadFilesPage(ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
    }

    /**
     * Upload a file then continue
     */
    public void upload(String filename) {
        uploadFile(FIELD_UPLOAD_FILE, filename);
        clickLink(LINK_NEXT);
    }

    /**
     * Defaults to not uploading any files
     */
    @Override
    public EccoBasePage defaultAction() {
        upload(null);
        return referralPage;
    }

}
