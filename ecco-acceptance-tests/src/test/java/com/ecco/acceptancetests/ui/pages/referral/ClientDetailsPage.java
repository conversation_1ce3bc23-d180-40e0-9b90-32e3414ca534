package com.ecco.acceptancetests.ui.pages.referral;

import org.openqa.selenium.WebDriver;
import com.ecco.acceptancetests.ui.pages.BasePageObject;
import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public class ClientDetailsPage extends BasePageObject {

    private static final String FIELD_NATIONAL_INSURANCE = "ni";
    private static final String FIELD_FIRSTNAME = "contact.firstName";
    private static final String FIELD_LASTNAME = "contact.lastName";
    private static final String FIELD_BIRTHDATE_DAY = "birthDate.day";
    private static final String FIELD_BIRTHDATE_MONTH = "birthDate.month";
    private static final String FIELD_BIRTHDATE_YEAR = "birthDate.year";
    private static final String RADIO_FIELD_GENDER = "gender";
    private static final String FIELD_FIRST_LANGUAGE = "firstLanguage";
    private static final String FIELD_ETHNICITY = "ethnicOrigin";

    private static final String NEW_SUB_HEADER_TEXT = "no matches - a new client: please enter more details";

    ReferralViewPage referralPage;
    String url;
    public ClientDetailsPage(String url, ReferralViewPage referralPage, WebDriver webDriver) {
        super(url, webDriver);
        this.referralPage = referralPage;
        this.url = url;
    }

    public void verifyIsCurrentPageNewClient() {
        super.verifyIsCurrentPage();
        waitForIdContainingText("status-message", NEW_SUB_HEADER_TEXT);
//        WebElement bodyText = getWebDriver().findElement(By.tagName("body"));
//        assertTrue("Incorrect page found - expected to see text " + NEW_SUB_HEADER_TEXT,
//                bodyText.getText().contains(NEW_SUB_HEADER_TEXT));
    }

    public void enterDetails(String firstName, String lastName, String dobDay, String dobMonth, String dobYear,
            String gender, String niNumber, String language, String ethnicity) {
        setField(FIELD_NATIONAL_INSURANCE, niNumber);
        setField(FIELD_FIRSTNAME, firstName);
        setField(FIELD_LASTNAME, lastName);
        setSelection(FIELD_BIRTHDATE_DAY, dobDay);
        setSelection(FIELD_BIRTHDATE_MONTH, dobMonth);
        setSelection(FIELD_BIRTHDATE_YEAR, dobYear);
        setRadio(RADIO_FIELD_GENDER, gender);
        setSelection(FIELD_FIRST_LANGUAGE, language);
        setSelection(FIELD_ETHNICITY, ethnicity);
        clickButton(EccoBasePage.BUTTON_NEXT_ID);
    }

    // this default assumes a new referral
    public EccoBasePage defaultAction() {
        return null;
    }

}
