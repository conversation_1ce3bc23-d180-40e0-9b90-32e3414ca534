package com.ecco.acceptancetests.ui.pages.referral;

import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.BasePageObject;
import com.ecco.acceptancetests.ui.pages.WelcomePage;

public class ReferralsMenuPage extends BasePageObject {

    private static final String URL = "/dynamic/secure/menuReferral";


    private static final String LINK_BACK_TO_MAIN_MENU = "back to main menu";

    public ReferralsMenuPage(WebDriver webDriver) {
        super(URL, webDriver);
    }


    public WelcomePage backToMainMenu() {
        clickLink(LINK_BACK_TO_MAIN_MENU);
        return new WelcomePage(getWebDriver());
    }

}
