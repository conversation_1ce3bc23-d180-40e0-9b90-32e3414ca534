package com.ecco.webApi.controllers;

import com.ecco.config.service.SettingsService;
import com.ecco.dao.EvidenceSupportWorkRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.infrastructure.web.RequestUtils;
import com.ecco.dom.BaseEntity;
import com.ecco.infrastructure.web.WebSlice;
import org.jetbrains.annotations.Contract;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

//@ReadOnlyTransaction
@WebSlice("api")
public abstract class BaseWebApiController {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    protected EvidenceSupportWorkRepository supportWorkRepository;
    @Autowired
    protected ServiceRecipientCommandRepository commandRepository;
    @Autowired
    protected SettingsService settingsService;
    @Autowired
    protected MessageSource messageSource;

    protected BaseWebApiController() {
        super();
    }

    @Contract("null, _ -> fail")
    protected void throwNotFoundIfNull(Object entity, Object id) {
        if (entity == null) {
            throw new NotFoundException(id);
        }
    }

    public static void throwForExistingOrTooManyMatches(List<? extends BaseEntity<?>> matches) {

        if (matches.size() == 0) {
            return;
        }

        if (matches.size() == 1) {
            throw new EntityAlreadyExistsException(matches.get(0).getId());
        }

        throw new RuntimeException("Duplicates exist and shouldn't for " + matches.get(0).getClass().getSimpleName());
    }

    /** @deprecated Migrate to {@link RequestUtils} */
    @Deprecated
    static public void cacheModerately(HttpServletResponse response) {
        RequestUtils.cacheModerately(response);
    }

    /** @deprecated Migrate to {@link RequestUtils} */
    @Deprecated
    static public void cacheForXHours(int numHours, HttpServletResponse response) {
        RequestUtils.cacheForXHours(numHours, response);
    }

    /** @deprecated Migrate to {@link RequestUtils} */
    @Deprecated
    static public void cacheForXSecs(int numSeconds, HttpServletResponse response) {
        RequestUtils.cacheForXSecs(numSeconds, response);
    }

    /** @deprecated Migrate to {@link RequestUtils} */
    @Deprecated
    static public void dontCache(HttpServletResponse response) {
        RequestUtils.dontCache(response);
    }

    /** For use with Last-Modified */
    protected void mustRevalidate(HttpServletResponse response) {
        response.setHeader("Cache-Control","must-revalidate, max-age=0");
        response.setHeader("Expires","-1");
    }

    /**
     * Returns true if can return a 304, having checked appropriate tables for latest entry.
     * Currently modified means "any newer command which relates to this service recipient"
     * <br>
     * NOTE: Do NOT set {@link ResponseStatus} on the calling request, as this will override the 304.
     *
     * TODO return false always if we've not switched to command based forms */
    protected boolean isUnmodifiedAndSetLastModified(int serviceRecipientId, WebRequest request) {
        boolean isUnmodified = request.checkNotModified(getLatestCommandTimestamp(serviceRecipientId));
        if (!isUnmodified) {
            mustRevalidate(((ServletWebRequest) request).getResponse());
        }
        return isUnmodified;
    }

    /**
     * NOTE: Checks commands and support work latest date.  DO NOT implement caching on risk history yet. Wait until
     * we're fully command driven.
     */
    private long getLatestCommandTimestamp(int serviceRecipientId) {
        Long latestCommand = commandRepository.findFirst1ByServiceRecipientIdOrderByCreatedDesc(serviceRecipientId)
                .map( cmd -> cmd.getCreated().getMillis() )
                .orElse(1L);

        Long latestWork = supportWorkRepository.findFirst1ByServiceRecipientIdOrderByCreatedDesc(serviceRecipientId)
            .map( cmd -> cmd.getCreated().getMillis() )
            .orElse(1L);
        return latestCommand > latestWork ? latestCommand : latestWork;
    }
}
